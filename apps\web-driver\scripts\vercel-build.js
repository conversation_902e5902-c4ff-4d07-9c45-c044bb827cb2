#!/usr/bin/env node

/**
 * Professional Vercel Build Script for web-driver in Turborepo Monorepo
 *
 * This script ensures proper dependency resolution and build order
 * for Vercel deployments in monorepo environments.
 *
 * Enterprise Features:
 * - Validates workspace dependencies
 * - Ensures proper build order
 * - Provides detailed logging
 * - <PERSON>les error cases gracefully
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting Professional Vercel Build for web-driver...');
console.log('📍 Current working directory:', process.cwd());
console.log('📍 Script directory:', __dirname);

// Determine the correct base directory
const baseDir = process.cwd().includes('apps/web-driver')
  ? process.cwd()
  : path.resolve(__dirname, '..');

const monorepoRoot = path.resolve(baseDir, '../..');

console.log('📍 Base directory:', baseDir);
console.log('📍 Monorepo root:', monorepoRoot);

// Validate workspace dependencies first
console.log('🔍 Validating workspace dependencies...');
const workspaceDeps = ['shared-ui', 'api-client', 'business-logic', 'config', 'firebase-config', 'shared-types', 'shared-utils'];

for (const dep of workspaceDeps) {
  const depPath = path.resolve(monorepoRoot, 'packages', dep);
  if (!fs.existsSync(depPath)) {
    console.error(`❌ Workspace dependency missing: ${dep} at ${depPath}`);
    process.exit(1);
  }

  const depPackageJson = path.resolve(depPath, 'package.json');
  if (!fs.existsSync(depPackageJson)) {
    console.error(`❌ Package.json missing for: ${dep}`);
    process.exit(1);
  }

  console.log(`✅ Workspace dependency found: ${dep}`);
}

// Verify critical files exist
const criticalFiles = [
  'src/lib/firebase.ts',
  'src/lib/firebase-admin.ts',
  'src/lib/database/users.ts',
  'src/contexts/AuthContext.tsx'
];

console.log('📋 Verifying critical files...');
for (const file of criticalFiles) {
  const filePath = path.resolve(baseDir, file);
  console.log(`🔍 Checking: ${filePath}`);
  if (!fs.existsSync(filePath)) {
    console.error(`❌ Critical file missing: ${file}`);
    console.error(`   Expected at: ${filePath}`);

    // Try alternative paths for debugging
    const altPath1 = path.resolve(process.cwd(), file);
    const altPath2 = path.resolve(__dirname, '..', file);
    console.log(`🔍 Alternative path 1: ${altPath1} - exists: ${fs.existsSync(altPath1)}`);
    console.log(`🔍 Alternative path 2: ${altPath2} - exists: ${fs.existsSync(altPath2)}`);

    // List directory contents for debugging
    const srcDir = path.resolve(baseDir, 'src');
    if (fs.existsSync(srcDir)) {
      console.log(`📁 Contents of ${srcDir}:`, fs.readdirSync(srcDir));
      const libDir = path.resolve(srcDir, 'lib');
      if (fs.existsSync(libDir)) {
        console.log(`📁 Contents of ${libDir}:`, fs.readdirSync(libDir));
      }
    }

    process.exit(1);
  } else {
    console.log(`✅ Found: ${file}`);
  }
}

// Verify TypeScript configuration
const tsconfigPath = path.resolve(baseDir, 'tsconfig.json');
if (!fs.existsSync(tsconfigPath)) {
  console.error('❌ tsconfig.json not found at:', tsconfigPath);
  process.exit(1);
}

const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
if (!tsconfig.compilerOptions.paths || !tsconfig.compilerOptions.paths['@/*']) {
  console.error('❌ TypeScript path mapping not configured');
  process.exit(1);
}

console.log('✅ TypeScript configuration verified');

// Verify Next.js configuration
const nextConfigPath = path.resolve(baseDir, 'next.config.ts');
if (!fs.existsSync(nextConfigPath)) {
  console.error('❌ next.config.ts not found at:', nextConfigPath);
  process.exit(1);
}

console.log('✅ Next.js configuration verified');

// Set Vercel-specific environment variables
process.env.VERCEL = '1';
process.env.NEXT_TELEMETRY_DISABLED = '1';
process.env.SKIP_ENV_VALIDATION = '1';
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

console.log('🎯 Environment variables set for Vercel build');

// Critical: Apply styled-jsx patch before build
console.log('🔧 Applying styled-jsx React 19 compatibility patch...');
try {
  const { execSync } = require('child_process');

  // Apply patch multiple times to ensure it works in Vercel environment
  execSync('node scripts/patch-styled-jsx.js', {
    cwd: baseDir,
    stdio: 'inherit'
  });

  // Also try to patch from monorepo root
  try {
    execSync('node apps/web-driver/scripts/patch-styled-jsx.js', {
      cwd: monorepoRoot,
      stdio: 'inherit'
    });
  } catch (rootError) {
    console.log('📝 Root patch attempt completed');
  }

  console.log('✅ styled-jsx patch applied successfully');
} catch (error) {
  console.warn('⚠️ styled-jsx patch failed, continuing...', error.message);
}

// Verify node_modules structure
const nodeModulesPath = path.resolve(baseDir, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.warn('⚠️ Local node_modules not found, checking workspace root...');
  const workspaceNodeModules = path.resolve(baseDir, '../../node_modules');
  if (!fs.existsSync(workspaceNodeModules)) {
    console.error('❌ No node_modules found');
    process.exit(1);
  } else {
    console.log('✅ Workspace node_modules found');
  }
} else {
  console.log('✅ Local node_modules found');
}

console.log('✅ Dependencies verified');

console.log('🎉 Vercel build preparation completed successfully!');
console.log('📦 Starting Next.js build...');

// Execute the actual build
try {
  const { execSync } = require('child_process');
  execSync('pnpm run build', {
    cwd: baseDir,
    stdio: 'inherit',
    env: {
      ...process.env,
      VERCEL: '1',
      NEXT_TELEMETRY_DISABLED: '1',
      SKIP_ENV_VALIDATION: '1',
      NODE_OPTIONS: '--max-old-space-size=4096'
    }
  });
  console.log('🎉 Build completed successfully!');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
