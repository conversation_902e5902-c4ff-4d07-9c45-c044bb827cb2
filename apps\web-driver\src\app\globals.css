@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
}

/* Fix placeholder text contrast globally */
::placeholder {
  color: #6b7280 !important; /* text-gray-500 - better contrast than default */
  opacity: 1 !important;
}

/* Fix input text contrast */
input, textarea, select {
  color: #111827 !important; /* text-gray-900 - high contrast */
}

/* Fix disabled input contrast */
input:disabled, textarea:disabled, select:disabled {
  color: #6b7280 !important; /* text-gray-500 - readable but clearly disabled */
}

/* Custom component styles */
.btn-primary {
  background-color: #f3a823;
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #ef7b06;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-outline {
  border: 2px solid #f3a823;
  color: #f3a823;
  background-color: transparent;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.btn-outline:hover {
  background-color: #f3a823;
  color: white;
}

.card {
  @apply bg-white rounded-lg shadow-md overflow-hidden;
}

.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.2s;
  color: #111827; /* text-gray-900 for better contrast */
}

.input-field::placeholder {
  color: #6b7280; /* text-gray-500 for better contrast */
}

.input-field:focus {
  ring: 2px solid #f3a823;
  border-color: transparent;
  box-shadow: 0 0 0 2px #f3a823;
}

.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Professional Scrollbar Styles for Admin Sidebar */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Hide scrollbar for mobile while keeping functionality */
@media (max-width: 1024px) {
  .scrollbar-thin {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-thin::-webkit-scrollbar {
    display: none;
  }
}

/* Smooth scrolling for better UX */
.scrollbar-thin {
  scroll-behavior: smooth;
}

/* Collapsible Sidebar Enhancements */
.sidebar-tooltip {
  transform: translateX(-50%);
  white-space: nowrap;
  font-size: 0.75rem;
  z-index: 9999;
}

/* Smooth transitions for sidebar collapse */
.sidebar-transition {
  transition: width 0.3s ease-in-out, margin-left 0.3s ease-in-out;
}

/* Enhanced hover effects for collapsed sidebar icons */
.collapsed-sidebar-item:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* Interactive collapsed sidebar enhancements */
.collapsed-category-button {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.collapsed-category-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.collapsed-category-button:active {
  transform: scale(0.98);
}

/* Enhanced tooltip for interactive collapsed sidebar */
.interactive-tooltip {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border: 1px solid #4b5563;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.interactive-tooltip::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid #1f2937;
}

/* Tooltip arrow positioning */
.tooltip-arrow {
  position: absolute;
  left: -4px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 8px;
  height: 8px;
  background-color: inherit;
}

/* Category text alignment fixes */
.category-text {
  word-break: break-word;
  hyphens: auto;
  line-height: 1.2;
  text-align: left;
}

/* Ensure proper alignment for multi-line category names */
.category-button {
  align-items: flex-start !important;
}

.category-icon {
  margin-top: 2px; /* Slight offset to align with first line of text */
}
