import type { NextConfig } from "next";
import path from "path";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  experimental: {
    // Remove deprecated experimental features for React 19
  },

  // CRITICAL: Disable styled-jsx at compiler level for React 19 compatibility
  compiler: {
    // Disable styled-jsx completely - this is the official Next.js way
    styledJsx: false,
    // Remove console logs in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },

  // Ensure proper module resolution for monorepo
  transpilePackages: ['shared-ui', 'shared-utils', 'shared-types'],
  // Move serverComponentsExternalPackages to root level
  serverExternalPackages: [],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // Environment variables to disable styled-jsx
  env: {
    DISABLE_STYLED_JSX: '1',
    STYLED_JSX_DISABLED: '1',
  },
  webpack: (config, { isServer, dev, buildId, defaultLoaders, nextRuntime, webpack }) => {
    // Professional Vercel-compatible webpack configuration
    const srcPath = path.resolve(__dirname, 'src');
    const emptyStyledJsxPath = path.resolve(__dirname, 'src/lib/empty-styled-jsx.js');

    // CRITICAL: Complete styled-jsx exclusion for React 19 compatibility
    // This is the official Next.js way to handle incompatible packages
    config.externals = config.externals || [];

    // Exclude styled-jsx from server-side bundles
    if (isServer) {
      config.externals.push('styled-jsx');
      config.externals.push('styled-jsx/style');
      config.externals.push('styled-jsx/css');
    }

    // Enhanced alias resolution for Vercel builds
    config.resolve.alias = {
      ...config.resolve.alias,
      // Primary alias
      '@': srcPath,
      // Explicit sub-path aliases for better Vercel compatibility
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/lib/firebase': path.resolve(__dirname, 'src/lib/firebase.ts'),
      '@/lib/firebase-admin': path.resolve(__dirname, 'src/lib/firebase-admin.ts'),
      '@/lib/database/users': path.resolve(__dirname, 'src/lib/database/users.ts'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/contexts': path.resolve(__dirname, 'src/contexts'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/app': path.resolve(__dirname, 'src/app'),
      '@/hooks': path.resolve(__dirname, 'src/hooks'),
      '@/utils': path.resolve(__dirname, 'src/utils'),

      // CRITICAL: styled-jsx React 19 compatibility fix
      // Force all styled-jsx imports to use our empty implementation
      'styled-jsx': emptyStyledJsxPath,
      'styled-jsx/style': emptyStyledJsxPath,
      'styled-jsx/css': emptyStyledJsxPath,
      'styled-jsx/macro': emptyStyledJsxPath,
      'styled-jsx/babel': emptyStyledJsxPath,
      'styled-jsx/webpack': emptyStyledJsxPath,
    };

    // CRITICAL: Add webpack plugin to completely ignore styled-jsx
    config.plugins = config.plugins || [];
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^styled-jsx/,
        contextRegExp: /./,
      })
    );

    // Add NormalModuleReplacementPlugin for styled-jsx
    config.plugins.push(
      new webpack.NormalModuleReplacementPlugin(
        /^styled-jsx$/,
        emptyStyledJsxPath
      ),
      new webpack.NormalModuleReplacementPlugin(
        /^styled-jsx\/style$/,
        emptyStyledJsxPath
      ),
      new webpack.NormalModuleReplacementPlugin(
        /^styled-jsx\/css$/,
        emptyStyledJsxPath
      )
    );

    // Enhanced file extension resolution
    config.resolve.extensions = ['.tsx', '.ts', '.jsx', '.js', '.mjs', '.json'];

    // Vercel-specific module resolution
    config.resolve.modules = [
      path.resolve(__dirname, 'src'),
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
      'node_modules'
    ];

    // Disable symlinks for Vercel compatibility
    config.resolve.symlinks = false;

    // Ensure case-sensitive resolution for Vercel
    config.resolve.cacheWithContext = false;

    // Firebase-specific optimizations and module resolution
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        http2: false,
        dns: false,
        child_process: false,
        os: false,
        path: false,
        stream: false,
        util: false,
        url: false,
        buffer: false,
        querystring: false,
      };
    }



    // Firebase v11 module resolution fixes
    config.resolve.alias = {
      ...config.resolve.alias,
      // Ensure Firebase modules resolve correctly
      '@firebase/app': require.resolve('@firebase/app'),
      '@firebase/auth': require.resolve('@firebase/auth'),
      '@firebase/firestore': require.resolve('@firebase/firestore'),
      '@firebase/messaging': require.resolve('@firebase/messaging'),
      '@firebase/storage': require.resolve('@firebase/storage'),

      // Additional styled-jsx aliases for comprehensive coverage
      'styled-jsx/server': emptyStyledJsxPath,
      'styled-jsx/style.js': emptyStyledJsxPath,
      'styled-jsx/css.js': emptyStyledJsxPath,
    };

    return config;
  },
  turbopack: {
    // Turbopack configuration to complement webpack setup
    rules: {
      // Add any custom loader rules here if needed
    },
    resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.mjs', '.json'],
    resolveAlias: {
      '@': path.resolve(__dirname, 'src'),
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/contexts': path.resolve(__dirname, 'src/contexts'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/app': path.resolve(__dirname, 'src/app'),
      '@/hooks': path.resolve(__dirname, 'src/hooks'),
      '@/utils': path.resolve(__dirname, 'src/utils'),

      // Turbopack styled-jsx aliases
      'styled-jsx': path.resolve(__dirname, 'src/lib/empty-styled-jsx.js'),
      'styled-jsx/style': path.resolve(__dirname, 'src/lib/empty-styled-jsx.js'),
      'styled-jsx/css': path.resolve(__dirname, 'src/lib/empty-styled-jsx.js'),
    },
  },
};

export default nextConfig;
