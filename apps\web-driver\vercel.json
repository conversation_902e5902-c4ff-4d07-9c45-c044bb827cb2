{"version": 2, "framework": "nextjs", "buildCommand": "cd ../.. && pnpm build --filter=web-driver", "outputDirectory": ".next", "installCommand": "cd ../.. && pnpm install --frozen-lockfile", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"VERCEL": "1", "NODE_VERSION": "20", "NEXT_TELEMETRY_DISABLED": "1"}, "build": {"env": {"VERCEL": "1", "NEXT_TELEMETRY_DISABLED": "1", "SKIP_ENV_VALIDATION": "1", "NODE_OPTIONS": "--max-old-space-size=4096", "DISABLE_STYLED_JSX": "1"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}