import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from '@/contexts/AuthContext';

// Force dynamic rendering to prevent React 19 + Next.js 15 styled-jsx issues
export const dynamic = 'force-dynamic';
export const dynamicParams = true;

export const metadata: Metadata = {
  title: "Tap2Go Driver",
  description: "Driver panel for Tap2Go food delivery platform",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="font-inter">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
